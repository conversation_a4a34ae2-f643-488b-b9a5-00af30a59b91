<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Cards Works Page - All in One</title>
    
    <!-- 依赖库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Helvetica, Arial, sans-serif;
            background-color: #f2f2f2;
            overflow: hidden;
        }
        
        .page-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        .work-title {
            position: absolute;
            left: 2vw;
            bottom: 6vh;
            color: rgb(0, 0, 0);
            font-family: Helvetica, Inter;
            font-size: 13vh;
            font-weight: 400;
            letter-spacing: -2px;
            z-index: 1001;
        }
        
        .bottom-text-button {
            margin-top: 1vh;
            text-align: right;
            position: absolute;
            bottom: 4vh;
            left: 2.8vw;
            width: 22vw;
            height: 3vh;
            cursor: pointer;
            transition: transform 0.3s ease;
            font-weight: 200;
            z-index: 1002;
        }
        
        .bottom-text-button:hover {
            transform: scale(1.01);
        }
        
        #canvas-wrapper {
            width: 100vw;
            height: 100vh;
            position: absolute;
        }
        
        .preview-container {
            position: absolute;
            right: 0;
            top: 60%;
            transform: translateY(-50%);
            width: 35vw;
            height: 80vh;
            padding: 60px 40px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            z-index: 50;
            pointer-events: none;
        }
        
        .preview-container.active {
            pointer-events: auto;
        }
        
        .preview-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            transform: translateX(100%);
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            background-color: rgba(0,0,0,0);
        }
        
        .preview-container.active .preview-wrapper {
            transform: translateX(0);
        }
        
        .preview-border {
            position: absolute;
            right: 35vw;
            bottom: 2vh;
            width: 2px;
            height: 0;
            background-color: #000000;
            transition: height 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 51;
        }
        
        .preview-border.active {
            height: 88vh;
        }
        
        .preview-content {
            opacity: 0;
            transition: opacity 0.4s ease 0.2s;
            position: relative;
            z-index: 2;
        }
        
        .preview-content.active {
            opacity: 1;
        }
        
        .preview-image {
            width: 100%;
            height: 280px;
            border-radius: 4px;
            margin-bottom: 32px;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f0f0f0;
        }
        
        .preview-image img {
            width: auto;
            height: auto;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.6s ease;
        }
        
        .preview-image:hover img {
            transform: scale(1.05);
        }
        
        .preview-date {
            font-family: Helvetica;
            font-size: 13px;
            color: #999;
            letter-spacing: 1px;
            margin-bottom: 10px;
            font-weight: 400;
        }
        
        .preview-title {
            font-family: Helvetica;
            font-size: 32px;
            font-weight: 500;
            margin-bottom: 10px;
            line-height: 1.2;
            letter-spacing: -0.5px;
        }
        
        .preview-subtitle {
            font-family: Helvetica;
            font-size: 18px;
            font-weight: 400;
            margin-bottom: 10px;
            line-height: 1.4;
            color: #666;
            letter-spacing: -0.2px;
        }
        
        .preview-description {
            font-family: Helvetica;
            font-size: 16px;
            line-height: 1.8;
            color: #666;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            font-size: 18px;
            z-index: 1000;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .work-title {
                font-size: 8vh;
                left: 4vw;
                bottom: 8vh;
            }
            
            .preview-container {
                width: 90vw;
                right: 5vw;
                padding: 40px 20px;
            }
            
            .preview-border {
                right: 5vw;
            }
            
            .bottom-text-button {
                left: 4vw;
                width: 40vw;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 标题 -->
        <div class="work-title">Works</div>
        
        <!-- 底部链接按钮 -->
        <div class="bottom-text-button">
            <div style="position: relative; float: left; color: rgb(0, 0, 0); font-size: 1.8vh; text-align: left;">
                View More Projects
            </div>
            <div style="position: relative; float: right; color: rgb(0, 0, 0); font-size: 2.5vh; text-align: left;">
                →
            </div>
        </div>
        
        <!-- 3D Canvas容器 -->
        <div id="canvas-wrapper"></div>
        
        <!-- 预览边框 -->
        <div class="preview-border"></div>
        
        <!-- 预览容器 -->
        <div class="preview-container">
            <div class="preview-wrapper">
                <div class="preview-content">
                    <div class="preview-image">
                        <img alt="" src=""/>
                    </div>
                    <div class="preview-date">Date</div>
                    <h2 class="preview-title">Card Title</h2>
                    <h3 class="preview-subtitle">Card Subtitle</h3>
                    <p class="preview-description"></p>
                </div>
            </div>
        </div>
        
        <!-- 加载指示器 -->
        <div class="loading-indicator">Loading...</div>
    </div>
    
    <script>
        /**
         * 3D卡片展示模块
         */
        const CardsModule = (function() {
            // 私有变量
            let scene, camera, renderer;
            let cards = [];
            let cardsGroup;
            let raycaster, mouse;
            let selectedCard = null;
            let hoveredCard = null;
            let currentPreviewCard = null;
            let isAnimating = false;
            let isInFullscreenMode = false;

            // 卡片配置
            const TOTAL_CARDS = 12;
            const CARD_WIDTH = 3.2;
            const CARD_HEIGHT = 1.8;
            const CARD_DEPTH = 0.01;
            const CARD_SPACING = 0.6;
            const TEXTURE_WIDTH = 1024;
            const TEXTURE_HEIGHT = Math.round(TEXTURE_WIDTH * (CARD_HEIGHT / CARD_WIDTH));

            // 卡片数据 - 你可以修改这里的数据
            const cardData = [
                {
                    title: "Project Alpha",
                    subtitle: 'Web Development',
                    description: "A modern web application built with React and Node.js, featuring real-time collaboration and advanced data visualization.",
                    image: "https://via.placeholder.com/400x300/d73a49/ffffff?text=Project+Alpha",
                    date: '2024-01-15'
                },
                {
                    title: "Design System",
                    subtitle: 'UI/UX Design',
                    description: "Comprehensive design system with reusable components, design tokens, and detailed documentation for scalable product development.",
                    image: "https://via.placeholder.com/400x300/2196F3/ffffff?text=Design+System",
                    date: '2024-02-20'
                },
                {
                    title: "Mobile App",
                    subtitle: 'iOS & Android',
                    description: "Cross-platform mobile application with native performance, featuring offline capabilities and seamless synchronization.",
                    image: "https://via.placeholder.com/400x300/4CAF50/ffffff?text=Mobile+App",
                    date: '2024-03-10'
                },
                {
                    title: "Data Analytics",
                    subtitle: 'Machine Learning',
                    description: "Advanced analytics platform using machine learning algorithms to provide actionable insights from complex datasets.",
                    image: "https://via.placeholder.com/400x300/FF9800/ffffff?text=Data+Analytics",
                    date: '2024-04-05'
                },
                {
                    title: "E-commerce Platform",
                    subtitle: 'Full Stack',
                    description: "Complete e-commerce solution with payment integration, inventory management, and customer analytics dashboard.",
                    image: "https://via.placeholder.com/400x300/9C27B0/ffffff?text=E-commerce",
                    date: '2024-05-12'
                }
            ];

            /**
             * 图片预处理函数
             */
            function preprocessImage(imageSrc, targetWidth, targetHeight) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.crossOrigin = 'anonymous';

                    img.onload = function() {
                        const canvas = document.createElement('canvas');
                        canvas.width = targetWidth;
                        canvas.height = targetHeight;
                        const ctx = canvas.getContext('2d');

                        const scale = Math.max(targetWidth / img.width, targetHeight / img.height);
                        const scaledWidth = img.width * scale;
                        const scaledHeight = img.height * scale;
                        const offsetX = (targetWidth - scaledWidth) / 2;
                        const offsetY = (targetHeight - scaledHeight) / 2;

                        ctx.imageSmoothingEnabled = true;
                        ctx.imageSmoothingQuality = 'high';
                        ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);

                        const texture = new THREE.CanvasTexture(canvas);
                        texture.needsUpdate = true;
                        texture.minFilter = THREE.LinearMipmapLinearFilter;
                        texture.magFilter = THREE.LinearFilter;
                        texture.generateMipmaps = true;
                        texture.anisotropy = renderer.capabilities.getMaxAnisotropy();

                        resolve(texture);
                    };

                    img.onerror = function() {
                        reject(new Error('Failed to load image: ' + imageSrc));
                    };

                    img.src = imageSrc;
                });
            }

            /**
             * 初始化场景
             */
            function init(containerId = 'canvas-wrapper') {
                scene = new THREE.Scene();
                scene.background = null;

                camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.01, 1000);
                camera.position.set(-1, -1, 15);

                renderer = new THREE.WebGLRenderer({
                    antialias: true,
                    precision: 'highp',
                    powerPreference: 'high-performance',
                    alpha: true,
                });

                renderer.setClearAlpha(0);
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setPixelRatio(window.devicePixelRatio);
                renderer.outputEncoding = THREE.sRGBEncoding;

                const container = document.getElementById(containerId);
                if (container) {
                    container.appendChild(renderer.domElement);
                }

                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(5, 5, 7);
                scene.add(directionalLight);

                raycaster = new THREE.Raycaster();
                mouse = new THREE.Vector2();

                createCards();

                window.addEventListener('resize', onWindowResize);
                renderer.domElement.addEventListener('mousemove', onMouseMove);
                renderer.domElement.addEventListener('mouseleave', onMouseLeave);

                animate();
            }

            /**
             * 创建3D卡片
             */
            async function createCards() {
                const colors = [
                    0xd73a49, 0x2196F3, 0x4CAF50, 0xFF9800, 0x9C27B0,
                    0xd73a49, 0x2196F3, 0x4CAF50, 0xFF9800, 0x9C27B0,
                    0xd73a49, 0x2196F3
                ];

                const commonRotationY = Math.PI * 0.05;
                const commonRotationX = Math.PI * -0.02;

                cardsGroup = new THREE.Group();
                scene.add(cardsGroup);

                cardsGroup.position.set(-3.8, 0, 0.1);
                cardsGroup.rotation.y = Math.PI * 0.22;
                cardsGroup.rotation.x = Math.PI * 0.09;
                cardsGroup.scale.set(0.85, 0.85, 0.85);

                cardsGroup.userData = {
                    centerX: -3.8,
                    leftX: -7
                };

                let loadedTextures = 0;

                for (let i = 0; i < TOTAL_CARDS; i++) {
                    const geometry = new THREE.BoxGeometry(CARD_WIDTH, CARD_HEIGHT, CARD_DEPTH);
                    const dataIndex = (TOTAL_CARDS - 1 - i) % cardData.length;
                    const currentCardData = cardData[dataIndex];

                    const tempFrontMaterial = new THREE.MeshBasicMaterial({ color: colors[i % colors.length] });
                    const backMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });
                    const edgeMaterial = new THREE.MeshBasicMaterial({ color: 0xdddddd });

                    const materials = [
                        edgeMaterial, edgeMaterial, edgeMaterial, edgeMaterial,
                        tempFrontMaterial, backMaterial
                    ];

                    const card = new THREE.Mesh(geometry, materials);

                    const zOffset = i * CARD_SPACING;
                    const xOffset = i * 0.02;

                    card.position.set(xOffset, -10, zOffset);
                    card.rotation.set(commonRotationX, commonRotationY, 0);

                    card.userData = {
                        index: TOTAL_CARDS - 1 - i,
                        origX: xOffset,
                        origY: 0,
                        origZ: zOffset,
                        zIndex: i,
                        data: currentCardData
                    };

                    cardsGroup.add(card);
                    cards.push(card);

                    preprocessImage(currentCardData.image, TEXTURE_WIDTH, TEXTURE_HEIGHT)
                        .then(texture => {
                            const frontMaterial = new THREE.MeshBasicMaterial({
                                map: texture,
                                side: THREE.FrontSide
                            });
                            card.material[4] = frontMaterial;

                            loadedTextures++;
                            if (loadedTextures === TOTAL_CARDS) {
                                const loadingIndicator = document.querySelector('.loading-indicator');
                                if (loadingIndicator) {
                                    loadingIndicator.style.display = 'none';
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error loading texture:', error);
                            loadedTextures++;
                            if (loadedTextures === TOTAL_CARDS) {
                                const loadingIndicator = document.querySelector('.loading-indicator');
                                if (loadingIndicator) {
                                    loadingIndicator.style.display = 'none';
                                }
                            }
                        });
                }

                animateCardsEntry();
            }

            /**
             * 显示预览
             */
            function showPreview(card) {
                if (currentPreviewCard === card) return;

                currentPreviewCard = card;
                const data = card.userData.data;

                const previewContent = document.querySelector('.preview-content');
                const previewContainer = document.querySelector('.preview-container');
                const previewBorder = document.querySelector('.preview-border');

                gsap.to(cardsGroup.position, {
                    x: cardsGroup.userData.leftX,
                    duration: 0.5,
                    ease: "power2.inOut"
                });

                if (previewContainer) previewContainer.classList.add('active');
                if (previewBorder) previewBorder.classList.add('active');

                const previewImage = document.querySelector('.preview-image img');
                const previewDate = document.querySelector('.preview-date');
                const previewTitle = document.querySelector('.preview-title');
                const previewSubtitle = document.querySelector('.preview-subtitle');
                const previewDescription = document.querySelector('.preview-description');

                if (previewImage) previewImage.src = data.image;
                if (previewDate) previewDate.textContent = data.date;
                if (previewTitle) previewTitle.textContent = data.title;
                if (previewSubtitle) previewSubtitle.textContent = data.subtitle;
                if (previewDescription) previewDescription.textContent = data.description;

                if (previewContent) previewContent.classList.add('active');
            }

            /**
             * 隐藏预览
             */
            function hidePreview() {
                currentPreviewCard = null;
                const previewContent = document.querySelector('.preview-content');
                const previewContainer = document.querySelector('.preview-container');
                const previewBorder = document.querySelector('.preview-border');

                gsap.to(cardsGroup.position, {
                    x: cardsGroup.userData.centerX,
                    duration: 0.5,
                    ease: "power2.inOut"
                });

                if (previewContainer) previewContainer.classList.remove('active');
                if (previewBorder) previewBorder.classList.remove('active');
                if (previewContent) previewContent.classList.remove('active');
            }

            /**
             * 窗口大小调整处理
             */
            function onWindowResize() {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setPixelRatio(window.devicePixelRatio);
            }

            /**
             * 鼠标移动处理
             */
            function onMouseMove(event) {
                if (isAnimating || isInFullscreenMode) {
                    document.body.style.cursor = 'default';
                    return;
                }

                const rect = renderer.domElement.getBoundingClientRect();
                mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
                mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

                raycaster.setFromCamera(mouse, camera);
                const intersects = raycaster.intersectObjects(cards);

                // 重置所有卡片状态
                cards.forEach(card => {
                    if (card !== selectedCard) {
                        gsap.to(card.position, {
                            y: card.userData.origY,
                            duration: 0.5,
                            overwrite: true
                        });

                        gsap.to(card.scale, {
                            x: 1, y: 1, z: 1,
                            duration: 0.3,
                            overwrite: true
                        });
                    }
                });

                // 处理悬停效果
                if (intersects.length > 0) {
                    const newHoveredCard = intersects[0].object;
                    hoveredCard = newHoveredCard;

                    gsap.to(hoveredCard.position, {
                        y: hoveredCard.userData.origY + 0.8,
                        duration: 0.4,
                        overwrite: true
                    });

                    gsap.to(hoveredCard.scale, {
                        x: 1.05, y: 1.05, z: 1.05,
                        duration: 0.3,
                        overwrite: true
                    });

                    showPreview(hoveredCard);
                    document.body.style.cursor = 'pointer';
                } else {
                    if (hoveredCard) {
                        hidePreview();
                    }
                    hoveredCard = null;
                    document.body.style.cursor = 'default';
                }
            }

            /**
             * 鼠标离开处理
             */
            function onMouseLeave() {
                if (!isAnimating && !isInFullscreenMode && hoveredCard) {
                    hidePreview();
                    hoveredCard = null;

                    cards.forEach(card => {
                        gsap.to(card.position, {
                            y: card.userData.origY,
                            duration: 0.5,
                            overwrite: true
                        });

                        gsap.to(card.scale, {
                            x: 1, y: 1, z: 1,
                            duration: 0.3,
                            overwrite: true
                        });
                    });
                }
            }

            /**
             * 卡片入场动画
             */
            function animateCardsEntry() {
                isAnimating = true;

                cards.forEach((card) => {
                    card.position.y = -10;
                });

                const totalAnimationDuration = cards.length * 0.1 + 1.8;

                cards.forEach((card, index) => {
                    gsap.to(card.position, {
                        y: card.userData.origY,
                        duration: 1.8,
                        delay: index * 0.1,
                        ease: "elastic.out(1, 0.5)"
                    });
                });

                setTimeout(() => {
                    isAnimating = false;
                }, totalAnimationDuration * 1000);
            }

            /**
             * 动画循环
             */
            function animate() {
                requestAnimationFrame(animate);
                renderer.render(scene, camera);
            }

            return {
                init: init
            };
        })();

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            CardsModule.init();
        });
    </script>
</body>
</html>
