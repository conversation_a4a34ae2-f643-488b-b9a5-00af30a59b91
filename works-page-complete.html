<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Cards Works Page</title>
    
    <!-- 依赖库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Helvetica, Arial, sans-serif;
            background-color: #f2f2f2;
            overflow: hidden;
        }
        
        /* 页面容器 */
        .page-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        /* 标题样式 */
        .work-title {
            position: absolute;
            left: 2vw;
            bottom: 6vh;
            color: rgb(0, 0, 0);
            font-family: Helvetica, Inter;
            font-size: 13vh;
            font-weight: 400;
            letter-spacing: -2px;
            z-index: 1001;
        }
        
        /* 底部按钮 */
        .bottom-text-button {
            margin-top: 1vh;
            text-align: right;
            position: absolute;
            bottom: 4vh;
            left: 2.8vw;
            width: 22vw;
            height: 3vh;
            cursor: pointer;
            transition: transform 0.3s ease;
            font-weight: 200;
            z-index: 1002;
        }
        
        .bottom-text-button:hover {
            transform: scale(1.01);
        }
        
        /* Canvas容器 */
        #canvas-wrapper {
            width: 100vw;
            height: 100vh;
            position: absolute;
        }
        
        /* 预览容器 */
        .preview-container {
            position: absolute;
            right: 0;
            top: 60%;
            transform: translateY(-50%);
            width: 35vw;
            height: 80vh;
            padding: 60px 40px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            z-index: 50;
            pointer-events: none;
        }
        
        .preview-container.active {
            pointer-events: auto;
        }
        
        .preview-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            transform: translateX(100%);
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            background-color: rgba(0,0,0,0);
        }
        
        .preview-container.active .preview-wrapper {
            transform: translateX(0);
        }
        
        .preview-border {
            position: absolute;
            right: 35vw;
            bottom: 2vh;
            width: 2px;
            height: 0;
            background-color: #000000;
            transition: height 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 51;
        }
        
        .preview-border.active {
            height: 88vh;
        }
        
        .preview-content {
            opacity: 0;
            transition: opacity 0.4s ease 0.2s;
            position: relative;
            z-index: 2;
        }
        
        .preview-content.active {
            opacity: 1;
        }
        
        .preview-image {
            width: 100%;
            height: 280px;
            border-radius: 4px;
            margin-bottom: 32px;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f0f0f0;
        }
        
        .preview-image img {
            width: auto;
            height: auto;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.6s ease;
        }
        
        .preview-image:hover img {
            transform: scale(1.05);
        }
        
        .preview-date {
            font-family: Helvetica;
            font-size: 13px;
            color: #999;
            letter-spacing: 1px;
            margin-bottom: 10px;
            font-weight: 400;
        }
        
        .preview-title {
            font-family: Helvetica;
            font-size: 32px;
            font-weight: 500;
            margin-bottom: 10px;
            line-height: 1.2;
            letter-spacing: -0.5px;
        }
        
        .preview-subtitle {
            font-family: Helvetica;
            font-size: 18px;
            font-weight: 400;
            margin-bottom: 10px;
            line-height: 1.4;
            color: #666;
            letter-spacing: -0.2px;
        }
        
        .preview-description {
            font-family: Helvetica;
            font-size: 16px;
            line-height: 1.8;
            color: #666;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        /* 加载指示器 */
        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            font-size: 18px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 标题 -->
        <div class="work-title">Works</div>
        
        <!-- 底部链接按钮 -->
        <div class="bottom-text-button">
            <div style="position: relative; float: left; color: rgb(0, 0, 0); font-size: 1.8vh; text-align: left;">
                View More Projects
            </div>
            <div style="position: relative; float: right; color: rgb(0, 0, 0); font-size: 2.5vh; text-align: left;">
                →
            </div>
        </div>
        
        <!-- 3D Canvas容器 -->
        <div id="canvas-wrapper"></div>
        
        <!-- 预览边框 -->
        <div class="preview-border"></div>
        
        <!-- 预览容器 -->
        <div class="preview-container">
            <div class="preview-wrapper">
                <div class="preview-content">
                    <div class="preview-image">
                        <img alt="" src=""/>
                    </div>
                    <div class="preview-date">Date</div>
                    <h2 class="preview-title">Card Title</h2>
                    <h3 class="preview-subtitle">Card Subtitle</h3>
                    <p class="preview-description"></p>
                </div>
            </div>
        </div>
        
        <!-- 加载指示器 -->
        <div class="loading-indicator">Loading...</div>
    </div>
    
    <script src="works-page-script.js"></script>
</body>
</html>
