<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo变形演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        .scroll-container {
            height: 100vh;
            overflow-y: auto;
            scroll-snap-type: y mandatory;
        }

        .page-section {
            width: 100%;
            height: 100vh;
            position: relative;
            scroll-snap-align: start;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            font-weight: bold;
        }

        .page-section:nth-child(1) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .page-section:nth-child(2) {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .page-section:nth-child(3) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .page-section:nth-child(4) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        /* Logo变形容器 */
        #morphing-logo {
            position: fixed;
            z-index: 1000;
            transition: all 0.1s ease-out;
        }

        #morphing-logo svg {
            width: 100%;
            height: 100%;
        }

        /* 页面标题 */
        .page-title {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 1.5rem;
            margin-top: 1rem;
            opacity: 0.8;
        }

        /* 文字变形效果 */
        .morphing-text {
            position: fixed;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            font-size: 4rem;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 999;
            transition: all 0.3s ease-out;
            letter-spacing: 0px;
        }

        /* 字符独立变形 */
        .char {
            display: inline-block;
            transition: all 0.3s ease-out;
            transform-origin: center;
        }

        /* 流动文字背景 */
        .flowing-text {
            position: fixed;
            top: 60%;
            left: 0;
            width: 100%;
            font-size: 2rem;
            color: rgba(255,255,255,0.3);
            white-space: nowrap;
            animation: flowRight 15s linear infinite;
            z-index: 1;
        }

        @keyframes flowRight {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 滚动指示器 */
        .scroll-indicator {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 1rem;
            animation: bounce 2s infinite;
            z-index: 999;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        /* 导航点 */
        .nav-dots {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 999;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: white;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <!-- Logo变形容器 -->
    <div id="morphing-logo">
        <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <!-- 主要形状 - 从圆形变为方形 -->
            <path id="main-shape" d="M100,20 A80,80 0 1,1 100,20 Z" fill="#ffffff" opacity="0.9"/>

            <!-- 内部装饰 - 从圆点变为方块 -->
            <circle id="inner-decoration" cx="100" cy="100" r="25" fill="#667eea"/>

            <!-- 文字标识 -->
            <text id="logo-text" x="100" y="110" text-anchor="middle" fill="#333" font-family="Arial" font-weight="bold" font-size="16">LOGO</text>
        </svg>
    </div>

    <!-- 变形文字 -->
    <div id="morphing-text" class="morphing-text">TRANSFORM</div>

    <!-- 流动背景文字 -->
    <div class="flowing-text" id="flowing-text">DESIGN • DEVELOP • CREATE • INNOVATE • </div>

    <!-- 滚动容器 -->
    <div class="scroll-container" id="scroll-container">
        <!-- 第1页 -->
        <section class="page-section">
            <div class="page-title">
                <div>欢迎来到Logo变形演示</div>
                <div class="page-subtitle">观察左上角的Logo变化</div>
            </div>
        </section>

        <!-- 第2页 -->
        <section class="page-section">
            <div class="page-title">
                <div>Logo正在变形</div>
                <div class="page-subtitle">从圆形到方形的过渡</div>
            </div>
        </section>

        <!-- 第3页 -->
        <section class="page-section">
            <div class="page-title">
                <div>变形完成</div>
                <div class="page-subtitle">现在是方形Logo</div>
            </div>
        </section>

        <!-- 第4页 -->
        <section class="page-section">
            <div class="page-title">
                <div>继续滚动</div>
                <div class="page-subtitle">Logo会继续变化</div>
            </div>
        </section>
    </div>

    <!-- 滚动指示器 -->
    <div class="scroll-indicator" id="scroll-indicator">
        ↓ 滚动查看效果
    </div>

    <!-- 导航点 -->
    <div class="nav-dots">
        <div class="nav-dot active" data-page="0"></div>
        <div class="nav-dot" data-page="1"></div>
        <div class="nav-dot" data-page="2"></div>
        <div class="nav-dot" data-page="3"></div>
    </div>

    <script>
        // Logo变形核心逻辑
        class LogoMorph {
            constructor() {
                this.scrollContainer = document.getElementById('scroll-container');
                this.morphingLogo = document.getElementById('morphing-logo');
                this.mainShape = document.getElementById('main-shape');
                this.innerDecoration = document.getElementById('inner-decoration');
                this.logoText = document.getElementById('logo-text');
                this.morphingText = document.getElementById('morphing-text');
                this.flowingText = document.getElementById('flowing-text');
                this.sections = document.querySelectorAll('.page-section');
                this.navDots = document.querySelectorAll('.nav-dot');
                this.scrollIndicator = document.getElementById('scroll-indicator');

                // 文字变形相关
                this.textWords = ['TRANSFORM', 'CREATIVE', 'DESIGN', 'DEVELOP'];
                this.currentWordIndex = 0;

                this.init();
            }

            init() {
                // 设置初始Logo状态
                this.setLogoState('big');

                // 初始化文字变形
                this.initTextMorph();

                // 绑定滚动事件
                this.scrollContainer.addEventListener('scroll', () => this.handleScroll());

                // 绑定导航点击事件
                this.navDots.forEach((dot, index) => {
                    dot.addEventListener('click', () => this.scrollToSection(index));
                });

                // 初始化第一个导航点
                this.updateNavDots(0);
            }

            // 初始化文字变形
            initTextMorph() {
                this.createCharElements(this.textWords[0]);
            }

            // 创建字符元素
            createCharElements(word) {
                this.morphingText.innerHTML = '';
                for (let i = 0; i < word.length; i++) {
                    const char = document.createElement('span');
                    char.className = 'char';
                    char.textContent = word[i];
                    char.style.animationDelay = `${i * 0.1}s`;
                    this.morphingText.appendChild(char);
                }
            }

            // Logo状态配置
            getLogoStates() {
                return {
                    big: {
                        left: '50vw',
                        top: '50vh',
                        width: '200px',
                        height: '200px',
                        transform: 'translate(-50%, -50%)',
                        opacity: '0.9'
                    },
                    medium: {
                        left: '20vw',
                        top: '20vh',
                        width: '120px',
                        height: '120px',
                        transform: 'translate(-50%, -50%)',
                        opacity: '0.8'
                    },
                    small: {
                        left: '60px',
                        top: '60px',
                        width: '80px',
                        height: '80px',
                        transform: 'translate(-50%, -50%)',
                        opacity: '1'
                    },
                    mini: {
                        left: '40px',
                        top: '40px',
                        width: '60px',
                        height: '60px',
                        transform: 'translate(-50%, -50%)',
                        opacity: '1'
                    }
                };
            }

            // SVG路径配置
            getShapePaths() {
                return {
                    circle: "M100,20 A80,80 0 1,1 100,20 Z",
                    roundedSquare: "M40,30 Q30,30 30,40 L30,160 Q30,170 40,170 L160,170 Q170,170 170,160 L170,40 Q170,30 160,30 Z",
                    square: "M30,30 L170,30 L170,170 L30,170 Z",
                    diamond: "M100,30 L170,100 L100,170 L30,100 Z"
                };
            }

            // 设置Logo状态
            setLogoState(state) {
                const states = this.getLogoStates();
                const targetState = states[state];
                
                Object.keys(targetState).forEach(prop => {
                    this.morphingLogo.style[prop] = targetState[prop];
                });
            }

            // 插值函数
            interpolateValue(start, end, progress) {
                if (typeof start === 'string' && start.includes('px')) {
                    const startVal = parseFloat(start);
                    const endVal = parseFloat(end);
                    return startVal + (endVal - startVal) * progress + 'px';
                }
                
                if (typeof start === 'string' && (start.includes('vw') || start.includes('vh'))) {
                    const startVal = parseFloat(start);
                    const endVal = parseFloat(end);
                    const unit = start.includes('vw') ? 'vw' : 'vh';
                    return startVal + (endVal - startVal) * progress + unit;
                }
                
                if (typeof start === 'number' && typeof end === 'number') {
                    return start + (end - start) * progress;
                }
                
                return progress < 0.5 ? start : end;
            }

            // 路径插值
            interpolatePath(path1, path2, progress) {
                // 简化的路径插值 - 实际项目中需要更复杂的SVG路径解析
                const paths = this.getShapePaths();
                const pathKeys = Object.keys(paths);
                
                if (progress <= 0.33) {
                    return progress < 0.165 ? paths.circle : paths.roundedSquare;
                } else if (progress <= 0.66) {
                    return paths.square;
                } else {
                    return paths.diamond;
                }
            }

            // 处理滚动
            handleScroll() {
                const scrollPosition = this.scrollContainer.scrollTop;
                const windowHeight = window.innerHeight;
                const totalHeight = this.scrollContainer.scrollHeight - windowHeight;
                const progress = Math.min(scrollPosition / totalHeight, 1);
                
                // 更新Logo形状
                this.updateLogoMorph(progress);
                
                // 更新当前页面
                const currentPage = Math.floor(scrollPosition / windowHeight);
                this.updateNavDots(currentPage);
                
                // 隐藏滚动指示器
                if (scrollPosition > 50) {
                    this.scrollIndicator.style.opacity = '0';
                } else {
                    this.scrollIndicator.style.opacity = '1';
                }
            }

            // 更新Logo变形
            updateLogoMorph(progress) {
                const states = this.getLogoStates();
                const stateKeys = Object.keys(states);

                // 根据进度选择状态
                let fromState, toState, localProgress;

                if (progress <= 0.33) {
                    fromState = states.big;
                    toState = states.medium;
                    localProgress = progress / 0.33;
                } else if (progress <= 0.66) {
                    fromState = states.medium;
                    toState = states.small;
                    localProgress = (progress - 0.33) / 0.33;
                } else {
                    fromState = states.small;
                    toState = states.mini;
                    localProgress = (progress - 0.66) / 0.34;
                }

                // 插值Logo属性
                this.morphingLogo.style.left = this.interpolateValue(fromState.left, toState.left, localProgress);
                this.morphingLogo.style.top = this.interpolateValue(fromState.top, toState.top, localProgress);
                this.morphingLogo.style.width = this.interpolateValue(fromState.width, toState.width, localProgress);
                this.morphingLogo.style.height = this.interpolateValue(fromState.height, toState.height, localProgress);
                this.morphingLogo.style.opacity = this.interpolateValue(parseFloat(fromState.opacity), parseFloat(toState.opacity), localProgress);

                // 更新SVG形状
                const newPath = this.interpolatePath(null, null, progress);
                this.mainShape.setAttribute('d', newPath);

                // 更新内部装饰
                if (progress > 0.5) {
                    // 变为方形
                    this.innerDecoration.setAttribute('rx', '5');
                    this.innerDecoration.setAttribute('ry', '5');
                } else {
                    // 保持圆形
                    this.innerDecoration.removeAttribute('rx');
                    this.innerDecoration.removeAttribute('ry');
                }

                // 更新文字大小
                const fontSize = 16 - progress * 6;
                this.logoText.setAttribute('font-size', Math.max(fontSize, 8));

                // 更新文字变形
                this.updateTextMorph(progress);
            }

            // 更新文字变形
            updateTextMorph(progress) {
                // 根据进度选择单词
                const wordIndex = Math.floor(progress * this.textWords.length);
                const clampedIndex = Math.min(wordIndex, this.textWords.length - 1);

                if (clampedIndex !== this.currentWordIndex) {
                    this.currentWordIndex = clampedIndex;
                    this.createCharElements(this.textWords[clampedIndex]);
                }

                // 更新字符间距
                const letterSpacing = progress * 20; // 0px 到 20px
                this.morphingText.style.letterSpacing = `${letterSpacing}px`;

                // 更新字符位置和变形
                const chars = this.morphingText.querySelectorAll('.char');
                chars.forEach((char, index) => {
                    const charProgress = (progress + index * 0.1) % 1;

                    // 字符旋转
                    const rotation = charProgress * 360;

                    // 字符缩放
                    const scale = 1 + Math.sin(charProgress * Math.PI * 2) * 0.3;

                    // 字符垂直偏移
                    const translateY = Math.sin(charProgress * Math.PI * 4) * 20;

                    char.style.transform = `rotate(${rotation}deg) scale(${scale}) translateY(${translateY}px)`;

                    // 字符颜色变化
                    const hue = (charProgress * 360 + index * 30) % 360;
                    char.style.color = `hsl(${hue}, 70%, 80%)`;
                });

                // 更新流动文字速度
                const animationDuration = 15 - progress * 10; // 15s 到 5s
                this.flowingText.style.animationDuration = `${animationDuration}s`;
            }

            // 更新导航点
            updateNavDots(currentPage) {
                this.navDots.forEach((dot, index) => {
                    dot.classList.toggle('active', index === currentPage);
                });
            }

            // 滚动到指定页面
            scrollToSection(index) {
                const targetPosition = index * window.innerHeight;
                this.scrollContainer.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        }

        // 初始化Logo变形
        document.addEventListener('DOMContentLoaded', () => {
            new LogoMorph();
        });
    </script>
</body>
</html>
