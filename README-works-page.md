# 3D卡片展示页面 - 完整代码包

这是一个基于Three.js和GSAP的3D卡片展示页面，具有流畅的动画效果和交互体验。

## 文件结构

```
├── works-page-complete.html    # 主HTML文件
├── works-page-script.js        # JavaScript逻辑文件
└── README-works-page.md        # 说明文档
```

## 功能特点

### 🎯 核心功能
- **3D卡片展示**: 使用Three.js创建立体卡片效果
- **流畅动画**: GSAP驱动的高性能动画
- **悬停交互**: 鼠标悬停时卡片抬起和放大
- **预览面板**: 右侧滑出式详情预览
- **响应式设计**: 适配不同屏幕尺寸

### 🎨 视觉效果
- **入场动画**: 卡片从下方弹性进入
- **悬停效果**: 卡片抬起0.8单位，放大1.05倍
- **布局切换**: 悬停时卡片组左移，为预览腾出空间
- **边框动画**: 预览时右侧边框从下往上展开

## 技术栈

- **Three.js**: 3D图形渲染
- **GSAP**: 动画库
- **HTML5 Canvas**: 图片预处理
- **CSS3**: 样式和过渡效果

## 使用方法

### 1. 基本使用
直接打开 `works-page-complete.html` 即可看到效果。

### 2. 自定义卡片数据
在 `works-page-script.js` 中修改 `cardData` 数组：

```javascript
const cardData = [
    {
        title: "你的项目标题",
        subtitle: '项目副标题',
        description: "项目详细描述...",
        image: "图片URL或路径",
        date: '2024-01-01'
    },
    // 添加更多项目...
];
```

### 3. 调整卡片配置
修改卡片参数：

```javascript
const TOTAL_CARDS = 15;        // 卡片总数
const CARD_WIDTH = 3.2;        // 卡片宽度
const CARD_HEIGHT = 1.8;       // 卡片高度
const CARD_SPACING = 0.6;      // 卡片间距
```

### 4. 自定义颜色
修改卡片颜色数组：

```javascript
const colors = [
    0xd73a49,  // 红色
    0x2196F3,  // 蓝色
    0x4CAF50,  // 绿色
    0xFF9800,  // 橙色
    0x9C27B0,  // 紫色
];
```

## 配置选项

### 卡片组位置
```javascript
cardsGroup.userData = {
    centerX: -3.8,  // 中心位置
    leftX: -7       // 左侧位置（预览时）
};
```

### 相机设置
```javascript
camera.position.set(-1, -1, 15);  // 相机位置
```

### 动画参数
```javascript
// 悬停抬起高度
y: hoveredCard.userData.origY + 0.8

// 悬停缩放比例
x: 1.05, y: 1.05, z: 1.05

// 动画持续时间
duration: 0.4
```

## 依赖库

确保包含以下CDN链接：

```html
<!-- Three.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

<!-- GSAP -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 性能优化

### 已实现的优化
- 高精度渲染器配置
- 各向异性纹理过滤
- 设备像素比适配
- 图片预处理和缓存
- 动画覆盖防止冲突

### 建议优化
- 使用WebP格式图片
- 实现图片懒加载
- 添加性能监控
- 优化移动端体验

## 自定义样式

### 修改预览面板样式
```css
.preview-container {
    width: 35vw;        /* 预览面板宽度 */
    padding: 60px 40px; /* 内边距 */
}

.preview-title {
    font-size: 32px;    /* 标题字体大小 */
    font-weight: 500;   /* 标题字重 */
}
```

### 修改卡片颜色
```css
/* 在CSS中无法直接修改3D卡片颜色，需要在JavaScript中修改colors数组 */
```

## 常见问题

### Q: 图片不显示怎么办？
A: 检查图片URL是否正确，确保图片支持跨域访问。

### Q: 动画卡顿怎么办？
A: 减少卡片数量或降低纹理分辨率。

### Q: 如何添加点击事件？
A: 在 `onMouseMove` 函数中添加点击检测逻辑。

### Q: 如何适配移动端？
A: 添加触摸事件监听和响应式布局调整。

## 扩展功能建议

- 添加卡片点击全屏查看
- 实现卡片筛选和搜索
- 添加键盘导航支持
- 集成路由系统
- 添加加载进度条
- 实现虚拟滚动优化性能

## 许可证

MIT License - 可自由使用和修改。
